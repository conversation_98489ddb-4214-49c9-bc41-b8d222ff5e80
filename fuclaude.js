addEventListener('fetch', event => {
    event.respondWith(handleRequest(event.request));
});

// 主请求处理函数
async function handleRequest(request) {
    const url = new URL(request.url);

    // 处理登录相关的路径
    if (url.pathname === "/accounts" || url.pathname === "/chat") {
        if (request.method === "POST") {
            if (url.pathname === "/accounts") {
                return handleAccountSelection(request);
            } else {
                return handleFormSubmission(request);
            }
        } else {
            // 对于非POST请求，显示登录页面
            return displayPage("login");
        }
    }

    // 对于其他所有请求，返回 404
    return new Response('Not Found', { status: 404 });
}

// 处理用户提交的登录表单
async function handleFormSubmission(request) {
    const formData = await request.formData();
    try {
        const unique_name = await validateUser(formData);
        return displayPage("accountSelection", unique_name);
    } catch (error) {
        return displayPage("error", error.message);
    }
}

// 处理账户选择
async function handleAccountSelection(request) {
    const formData = await request.formData();
    const unique_name = formData.get("unique_name");
    const account_key = formData.get("account_key");

    try {
        // @ts-ignore
        const sessionKey = await cla.get(account_key);
        // @ts-ignore
        const ORIGINAL_WEBSITE = await cla.get("ORIGINAL_WEBSITE");

        // 请求 OAuth token
        const oauthResponse = await fetch(`${ORIGINAL_WEBSITE}/manage-api/auth/oauth_token`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                session_key: sessionKey,
                unique_name: unique_name
            }),
        });

        if (!oauthResponse.ok) {
            throw new Error('Failed to obtain OAuth token');
        }

        const oauthData = await oauthResponse.json();
        const loginUrl = `${ORIGINAL_WEBSITE}${oauthData.login_url}`;

        // 直接重定向到原始网站的登录URL
        return Response.redirect(loginUrl, 302);
    } catch (error) {
        return displayPage("error", error.message);
    }
}

// 验证用户身份
async function validateUser(formData) {
    // @ts-ignore
    const SITE_PASSWORD = await cla.get("SITE_PASSWORD") || "";
    const site_password = formData.get("site_password") || "";
    const unique_name = formData.get("username") || "";

    if (site_password !== SITE_PASSWORD) {
        throw new Error("访问密码错误");
    }

    return unique_name;
}

// 获取所有账户的键
async function getAccounts() {
    // @ts-ignore
    const list = await cla.list();
    return list.keys
        .map(key => key.name)
        .filter(name => name !== "SITE_PASSWORD" && name !== "ORIGINAL_WEBSITE" && name !== "SESSION_KEY");
}

// 显示页面
async function displayPage(type, data = '') {
    const style = `
    <style>
      body {
        font-family: Arial, sans-serif;
        background-color: #f4f4f4;
        padding: 20px;
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100vh;
        margin: 0;
      }
      .container {
        background: white;
        padding: 30px;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        max-width: 400px;
        width: 100%;
        text-align: center;
      }
      h1 {
        text-align: center;
        color: #333;
        margin-bottom: 30px;
      }
      label {
        display: block;
        margin-bottom: 10px;
        color: #666;
        font-size: 18px;
        text-align: left; /* 添加这一行使文本靠左显示 */
        font-weight: bold; /* 添加这一行使文本加粗 */
        font-size: 20px; /* 添加这一行增大字体 */
      }
      input[type="text"],
      input[type="password"],
      select {
        width: 100%;
        padding: 10px;
        margin-bottom: 20px;
        border: 1px solid #ddd;
        border-radius: 4px;
        box-sizing: border-box;
        font-size: 18px;
      }
      option {
        font-size: 18px; /* 增大下拉选择框内容的字体 */
      }
      button {
        width: 100%;
        padding: 10px;
        background-color: #000000;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 16px;
        font-weight: 600;
      }
      button:hover {
        background-color: #1e293b;
      }
    </style>
  `;

    let content = '';

    switch (type) {
        case "login":
            content = `
            <h1>欢迎使用Claude</h1>
            <form method="POST">
              <label for="username">用户名：</label>
              <input type="text" id="username" name="username" required autofocus>
              <label for="site_password">请输入本网站使用密码：</label>
              <input type="password" id="site_password" name="site_password" required>
              <button type="submit">访问使用</button>
            </form>
            <script>
              document.getElementById('username').focus();
            </script>
          `;
            break;

        case "accountSelection":
            const keys = await getAccounts();
            const accountOptions = keys.map(key => {
                const atIndex = key.indexOf('@');
                const visiblePart = key.substring(0, 3);
                const hiddenPart = '*'.repeat(atIndex - 3);
                const maskedKey = visiblePart + hiddenPart + key.substring(atIndex);
                return `<option value="${key}">${maskedKey}</option>`;
            }).join("");
            content = `
              <h1>选择账户</h1>
              <form method="POST" action="/accounts">
                <input type="hidden" name="unique_name" value="${data}">
                <select id="account_key" name="account_key">
                  ${accountOptions}
                </select>
                <button type="submit">开始使用</button>
              </form>
            `;
            break;

        case "error":
            content = `
            <h1>错误</h1>
            <p>${data}</p>
          `;
            break;
    }

    return new Response(`
    <!DOCTYPE html>
    <html lang="zh-cn">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Claude Access</title>
      ${style}
    </head>
    <body>
      <div class="container">
        ${content}
      </div>
    </body>
    </html>
  `, {
        headers: {
            "Content-Type": "text/html; charset=utf-8",
        },
    });
}