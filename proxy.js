/**
 * Welcome to Cloudflare Workers! This is your first worker.
 *
 * - Run "npm run dev" in your terminal to start a development server
 * - Open a browser tab at http://localhost:8787/ to see your worker in action
 * - Run "npm run deploy" to publish your worker
 *
 * Learn more at https://developers.cloudflare.com/workers/
 */


export default {
    async fetch(request, env) {
      const url = new URL(request.url);
      
      // 检查是否是 /accounts 或 /chat 路径
      if (url.pathname === "/accounts" || url.pathname === "/chat") {
        url.hostname = 'cfree.776778.xyz';
      } else {
        // 对于其他所有路径，更改主机名为 claude.776778.xyz
        // url.hostname = 'claude.776778.xyz';
        url.hostname = 'demo.fuclaude.com';
      }
      
      // 创建新的请求，保持其他所有参数不变
      return fetch(new Request(url, request));
    }
  }