# Cloudflare Worker 代理与鉴权项目

这个项目将代理功能与用户鉴权、密钥选择、OAuth流程整合到单一Cloudflare Worker脚本中。

## 功能

- 反向代理请求到目标网站
- 用户鉴权与OAuth流程
- 管理员密钥管理界面
- 支持HTTPS安全检查

## 开发

### 准备工作

1. 在Cloudflare控制台创建KV命名空间，获取KV ID
2. 更新`wrangler.toml`中的KV ID
3. 在KV命名空间中添加以下键值对:
   - `config:ORIGINAL_WEBSITE`: 目标网站URL，例如 `https://demo.fuclaude.com`
   - `config:SITE_PASSWORD`: 用户访问密码
   - `config:ADMIN_PASSWORD`: 管理员密码

### 安装依赖

```bash
npm install
```

### 本地开发

```bash
npm run dev
```

### 部署

```bash
npm run deploy
```

## 项目结构

- `worker.js`: 主Worker脚本
- `wrangler.toml`: Cloudflare Worker配置文件 