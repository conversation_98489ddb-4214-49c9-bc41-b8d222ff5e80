// 简单的路由处理函数
async function handleRequest(request, env, ctx) {
    // 首先检查HTTPS
    const httpsRedirect = httpsCheck(request);
    if (httpsRedirect) return httpsRedirect;

    const url = new URL(request.url);
    const path = url.pathname;

    // 根据路径分发请求 - 同时处理完全匹配和子路径
    if (path === '/admin' || path.startsWith('/admin/')) {
        return handleAdmin(request, env, ctx);
    } else if (path === '/user' || path.startsWith('/user/')) {
        return handleChat(request, env, ctx);
    } else {
        return handleDefault(request, env, ctx);
    }
}

// HTTPS检查中间件
const httpsCheck = (request) => {
    const url = new URL(request.url);
    if (url.protocol !== 'https:') {
        // 构建重定向URL（将http更改为https）
        const httpsUrl = request.url.replace(/^http:/, 'https:');
        return Response.redirect(httpsUrl, 301);
    }
    // 返回null继续处理请求
    return null;
};

// 管理员认证中间件
async function authenticateAdmin(request, env) {
    // 从请求头中获取密码
    const adminPassword = request.headers.get('X-Admin-Password');

    if (!adminPassword) {
        return {
            authenticated: false,
            response: new Response('未提供管理员密码', {
                status: 401,
                headers: { 'Content-Type': 'text/plain;charset=UTF-8' }
            })
        };
    }

    try {
        // 从KV获取存储的管理员密码
        const storedPassword = env && env.cla ?
            await env.cla.get('config:ADMIN_PASSWORD') :
            null;

        // 比较密码
        if (storedPassword && adminPassword === storedPassword) {
            return { authenticated: true };
        } else {
            return {
                authenticated: false,
                response: new Response('管理员密码错误', {
                    status: 401,
                    headers: { 'Content-Type': 'text/plain;charset=UTF-8' }
                })
            };
        }
    } catch (error) {
        return {
            authenticated: false,
            response: new Response(`认证过程出错: ${error.message}`, {
                status: 500,
                headers: { 'Content-Type': 'text/plain;charset=UTF-8' }
            })
        };
    }
}

// KV操作辅助函数
// 获取指定name的密钥详情
async function getKey(kv, name) {
    if (!kv) return null;

    const keyData = await kv.get(`key:${name}`);
    if (!keyData) return null;

    try {
        return JSON.parse(keyData);
    } catch (e) {
        console.error(`解析密钥数据出错: ${e.message}`);
        return null;
    }
}

// 获取所有密钥详情列表
async function listKeys(kv) {
    if (!kv) return [];

    const keys = [];
    const { keys: kvKeys } = await kv.list({ prefix: 'key:' });

    for (const { name } of kvKeys) {
        // 提取descriptive_name（去掉key:前缀）
        const descriptiveName = name.substring(4);
        const keyData = await getKey(kv, descriptiveName);

        if (keyData) {
            keys.push({
                name: descriptiveName,
                ...keyData
            });
        }
    }

    return keys;
}

// 添加新密钥
async function addKeytoKV(kv, name, apiKey, description) {
    if (!kv) throw new Error('KV存储未配置');
    if (!name) throw new Error('必须提供密钥名称');
    if (!apiKey) throw new Error('必须提供API密钥');

    // 检查密钥名称是否已存在
    const existing = await getKey(kv, name);
    if (existing) throw new Error('密钥名称已存在');

    // 创建密钥数据
    const keyData = {
        apiKey,
        description: description || '',
        addedTimestamp: Date.now(),
        enabled: true
    };

    // 存储密钥数据
    await kv.put(`key:${name}`, JSON.stringify(keyData));

    return { name, ...keyData };
}

// 更新密钥信息
async function updateKey(kv, name, updates) {
    if (!kv) throw new Error('KV存储未配置');
    if (!name) throw new Error('必须提供密钥名称');

    // 获取现有密钥
    const existing = await getKey(kv, name);
    if (!existing) throw new Error('密钥不存在');

    // 更新数据
    const updatedData = {
        ...existing,
        ...updates,
        // 保持原始添加时间戳不变
        addedTimestamp: existing.addedTimestamp
    };

    // 存储更新后的数据
    await kv.put(`key:${name}`, JSON.stringify(updatedData));

    return { name, ...updatedData };
}

// 删除密钥
async function deleteKey(kv, name) {
    if (!kv) throw new Error('KV存储未配置');
    if (!name) throw new Error('必须提供密钥名称');

    // 检查密钥是否存在
    const existing = await getKey(kv, name);
    if (!existing) throw new Error('密钥不存在');

    // 删除密钥
    await kv.delete(`key:${name}`);

    return true;
}

// 通用的反向代理函数
async function proxyRequest(request, targetHost) {
    const url = new URL(request.url);

    // 更改请求URL的主机名
    url.hostname = new URL(targetHost).hostname;

    // 创建新的请求对象，保持原始请求的所有参数
    const newRequest = new Request(url.toString(), {
        method: request.method,
        headers: request.headers,
        body: request.body,
        redirect: request.redirect,
    });

    // 发送新请求
    return fetch(newRequest);
}

// 处理/admin路径
async function handleAdmin(request, env, ctx) {
    const url = new URL(request.url);
    const path = url.pathname;

    // 静态资源路由
    if (path === '/admin/manage/style.css') {
        return new Response(adminManageCss, {
            headers: { 'Content-Type': 'text/css;charset=UTF-8' }
        });
    } else if (path === '/admin/manage/script.js') {
        return new Response(adminManageJs, {
            headers: { 'Content-Type': 'application/javascript;charset=UTF-8' }
        });
    }
    // 管理页面路由
    else if (path === '/admin/manage' || path === '/admin') {
        return new Response(adminManageHtml, {
            headers: { 'Content-Type': 'text/html;charset=UTF-8' }
        });
    }
    // 管理员API路由
    else if (path === '/admin/api/keys' || path.startsWith('/admin/api/keys/')) {
        // 管理员认证
        const auth = await authenticateAdmin(request, env);
        if (!auth.authenticated) {
            return auth.response;
        }

        try {
            // 获取KV
            const kv = env?.cla;
            if (!kv) {
                return new Response('KV存储未配置', {
                    status: 500,
                    headers: { 'Content-Type': 'text/plain;charset=UTF-8' }
                });
            }

            // 根据HTTP方法和路径处理请求
            if (request.method === 'GET' && path === '/admin/api/keys') {
                // 获取所有密钥
                const keys = await listKeys(kv);
                return new Response(JSON.stringify(keys), {
                    status: 200,
                    headers: { 'Content-Type': 'application/json;charset=UTF-8' }
                });
            }
            else if (request.method === 'POST' && path === '/admin/api/keys') {
                // 添加新密钥
                try {
                    const data = await request.json();

                    // 验证必填字段
                    if (!data.name || !data.apiKey) {
                        return new Response(JSON.stringify({
                            error: '缺少必要字段：name和apiKey为必填项'
                        }), {
                            status: 400,
                            headers: { 'Content-Type': 'application/json;charset=UTF-8' }
                        });
                    }

                    // 添加密钥
                    const result = await addKeytoKV(kv, data.name, data.apiKey, data.description);
                    return new Response(JSON.stringify(result), {
                        status: 201,
                        headers: { 'Content-Type': 'application/json;charset=UTF-8' }
                    });
                } catch (error) {
                    if (error.message === '密钥名称已存在') {
                        return new Response(JSON.stringify({ error: error.message }), {
                            status: 409, // Conflict
                            headers: { 'Content-Type': 'application/json;charset=UTF-8' }
                        });
                    } else {
                        return new Response(JSON.stringify({ error: error.message }), {
                            status: 400,
                            headers: { 'Content-Type': 'application/json;charset=UTF-8' }
                        });
                    }
                }
            }
            else if (request.method === 'PUT' && path.startsWith('/admin/api/keys/')) {
                // 更新密钥
                try {
                    // 从路径获取密钥名称
                    const keyName = path.substring('/admin/api/keys/'.length);
                    if (!keyName) {
                        return new Response(JSON.stringify({ error: '未指定密钥名称' }), {
                            status: 400,
                            headers: { 'Content-Type': 'application/json;charset=UTF-8' }
                        });
                    }

                    // 获取请求体
                    const updates = await request.json();

                    // 更新密钥
                    const result = await updateKey(kv, keyName, updates);
                    return new Response(JSON.stringify(result), {
                        status: 200,
                        headers: { 'Content-Type': 'application/json;charset=UTF-8' }
                    });
                } catch (error) {
                    if (error.message === '密钥不存在') {
                        return new Response(JSON.stringify({ error: error.message }), {
                            status: 404, // Not Found
                            headers: { 'Content-Type': 'application/json;charset=UTF-8' }
                        });
                    } else {
                        return new Response(JSON.stringify({ error: error.message }), {
                            status: 400,
                            headers: { 'Content-Type': 'application/json;charset=UTF-8' }
                        });
                    }
                }
            }
            else if (request.method === 'DELETE' && path.startsWith('/admin/api/keys/')) {
                // 删除密钥
                try {
                    // 从路径获取密钥名称
                    const keyName = path.substring('/admin/api/keys/'.length);
                    if (!keyName) {
                        return new Response(JSON.stringify({ error: '未指定密钥名称' }), {
                            status: 400,
                            headers: { 'Content-Type': 'application/json;charset=UTF-8' }
                        });
                    }

                    // 删除密钥
                    await deleteKey(kv, keyName);
                    return new Response(JSON.stringify({ success: true }), {
                        status: 200,
                        headers: { 'Content-Type': 'application/json;charset=UTF-8' }
                    });
                } catch (error) {
                    if (error.message === '密钥不存在') {
                        return new Response(JSON.stringify({ error: error.message }), {
                            status: 404, // Not Found
                            headers: { 'Content-Type': 'application/json;charset=UTF-8' }
                        });
                    } else {
                        return new Response(JSON.stringify({ error: error.message }), {
                            status: 400,
                            headers: { 'Content-Type': 'application/json;charset=UTF-8' }
                        });
                    }
                }
            } else {
                // 不支持的方法或路径
                return new Response(JSON.stringify({ error: '不支持的方法或路径' }), {
                    status: 405, // Method Not Allowed
                    headers: { 'Content-Type': 'application/json;charset=UTF-8' }
                });
            }
        } catch (error) {
            return new Response(JSON.stringify({ error: `处理请求时出错: ${error.message}` }), {
                status: 500,
                headers: { 'Content-Type': 'application/json;charset=UTF-8' }
            });
        }
    }

    // 其他admin路径
    else {
        return new Response('管理员功能正在开发中...', {
            headers: { 'Content-Type': 'text/plain;charset=UTF-8' }
        });
    }
}

// 处理/user路径
async function handleChat(request, env, ctx) {
    const url = new URL(request.url);
    const path = url.pathname;

    // 获取KV存储
    const kv = env?.cla;
    if (!kv) {
        return new Response('配置错误：KV存储未配置', {
            status: 500,
            headers: { 'Content-Type': 'text/plain;charset=UTF-8' }
        });
    }

    // 处理POST请求（表单提交）
    if (request.method === 'POST') {
        // 提取表单数据
        const formData = await request.formData();

        // 添加请求URL到表单数据
        if (!formData.has('request_url')) {
            formData.append('request_url', request.url);
        }

        // 检查是否是密钥选择请求
        if (formData.has('key_name')) {
            // 处理密钥选择 - OAuth流程
            return await handleKeySelection(formData, kv);
        } else {
            // 处理密码验证
            return await handlePasswordVerification(formData, kv);
        }
    }

    // 处理GET请求（显示登录页面）
    // 在HTML中添加隐藏的请求URL字段
    return new Response(userChatHtml.replace('</form>', `<input type="hidden" name="request_url" value="${request.url}"></form>`), {
        headers: { 'Content-Type': 'text/html;charset=UTF-8' }
    });
}

// 验证用户提交的站点密码
async function handlePasswordVerification(formData, kv) {
    const sitePassword = formData.get('site_password');
    const requestUrl = formData.get('request_url');

    try {
        // 从KV获取存储的站点密码
        const storedPassword = await kv.get('config:SITE_PASSWORD');

        // 验证密码
        if (!storedPassword || sitePassword !== storedPassword) {
            return new Response(generateErrorHtml('站点访问密码错误，请重试。'), {
                headers: { 'Content-Type': 'text/html;charset=UTF-8' }
            });
        }

        // 密码验证成功，显示密钥选择页面
        const keys = await listEnabledKeys(kv);
        return new Response(generateKeySelectionHtml(keys, requestUrl), {
            headers: { 'Content-Type': 'text/html;charset=UTF-8' }
        });
    } catch (error) {
        return new Response(generateErrorHtml(`验证过程出错: ${error.message}`), {
            headers: { 'Content-Type': 'text/html;charset=UTF-8' }
        });
    }
}

// 处理用户选择的密钥并触发OAuth流程
async function handleKeySelection(formData, kv) {
    const keyName = formData.get('key_name');
    // requestUrl 变量保留以备将来可能的使用，例如错误页面返回链接
    const requestUrl = formData.get('request_url');

    if (!keyName) {
        return new Response(generateErrorHtml('未选择密钥，请重试。'), {
            headers: { 'Content-Type': 'text/html;charset=UTF-8' }
        });
    }

    try {
        // 获取选择的密钥详情
        const keyData = await getKey(kv, keyName);
        if (!keyData) {
            return new Response(generateErrorHtml('所选密钥不存在或已被禁用。'), {
                headers: { 'Content-Type': 'text/html;charset=UTF-8' }
            });
        }

        // 获取目标网站URL
        const originalWebsite = await kv.get('config:ORIGINAL_WEBSITE');
        if (!originalWebsite) {
            return new Response(generateErrorHtml('配置错误：未找到目标网站URL。'), {
                headers: { 'Content-Type': 'text/html;charset=UTF-8' }
            });
        }

        // 请求OAuth Token
        const oauthResponse = await requestOAuthToken(keyData.apiKey, keyName, originalWebsite);

        // 获取登录URL路径部分
        const loginUrl = oauthResponse.login_url;

        // 验证登录URL格式
        if (!loginUrl || !loginUrl.startsWith('/')) {
            throw new Error(`无效的登录URL格式: ${loginUrl}`);
        }

        // 构建完整的原始网站登录URL，直接重定向到目标网站
        const fullLoginUrl = `${originalWebsite}${loginUrl}`;

        console.log(`OAuth直接重定向到原始网站: ${fullLoginUrl}`);
        console.log(`使用的API密钥: ${keyName}`);

        // 直接重定向到原始网站的登录URL，不通过Worker代理
        return Response.redirect(fullLoginUrl, 302);
    } catch (error) {
        return new Response(generateErrorHtml(`OAuth流程出错: ${error.message}`), {
            headers: { 'Content-Type': 'text/html;charset=UTF-8' }
        });
    }
}

// 获取所有启用的密钥
async function listEnabledKeys(kv) {
    const allKeys = await listKeys(kv);
    return allKeys.filter(key => key.enabled);
}

// 请求OAuth Token
async function requestOAuthToken(apiKey, keyName, originalWebsite) {
    const oauthEndpoint = `${originalWebsite}/manage-api/auth/oauth_token`;

    const response = await fetch(oauthEndpoint, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            session_key: apiKey,
            unique_name: keyName
        }),
    });

    if (!response.ok) {
        const text = await response.text();
        throw new Error(`OAuth服务返回错误 (${response.status}): ${text}`);
    }

    return await response.json();
}

// 生成带错误信息的HTML
function generateErrorHtml(errorMessage) {
    return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Claude API密钥选择 - 出错了</title>
    <style>
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }
        
        .container {
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
        }
        
        header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eaeaea;
        }
        
        h1 {
            color: #2c3e50;
            font-size: 24px;
        }
        
        .nav-button {
            display: inline-block;
            background-color: #3498db;
            color: white;
            padding: 8px 16px;
            border-radius: 4px;
            text-decoration: none;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        
        .nav-button:hover {
            background-color: #2980b9;
        }
        
        .card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 30px;
        }
        
        h2 {
            color: #34495e;
            margin-bottom: 20px;
            font-size: 20px;
        }
        
        .error-message {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .back-button {
            display: inline-block;
            background-color: #6c757d;
            color: white;
            padding: 10px 20px;
            border-radius: 4px;
            text-decoration: none;
            font-size: 16px;
            transition: background-color 0.3s;
        }
        
        .back-button:hover {
            background-color: #5a6268;
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>Claude API密钥选择</h1>
            <a href="/admin" class="nav-button">管理界面</a>
        </header>
        
        <div class="card">
            <h2>出错了</h2>
            <div class="error-message">${errorMessage}</div>
            <a href="/user" class="back-button">返回重试</a>
        </div>
    </div>
</body>
</html>`;
}

// 生成密钥选择页面HTML
function generateKeySelectionHtml(keys, requestUrl) {
    // 如果没有可用密钥
    if (!keys || keys.length === 0) {
        return generateErrorHtml('没有可用的API密钥，请联系管理员添加密钥。');
    }

    // 生成密钥选项HTML
    const keyOptions = keys.map(key => {
        return `<option value="${key.name}">${key.name} - ${key.description || '无描述'}</option>`;
    }).join('');

    return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Claude API密钥选择</title>
    <style>
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }
        
        .container {
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
        }
        
        header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eaeaea;
        }
        
        h1 {
            color: #2c3e50;
            font-size: 24px;
        }
        
        .nav-button {
            display: inline-block;
            background-color: #3498db;
            color: white;
            padding: 8px 16px;
            border-radius: 4px;
            text-decoration: none;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        
        .nav-button:hover {
            background-color: #2980b9;
        }
        
        .card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 30px;
        }
        
        h2 {
            color: #34495e;
            margin-bottom: 20px;
            font-size: 20px;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #555;
        }
        
        select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        button {
            width: 100%;
            padding: 12px;
            background-color: #2ecc71;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        button:hover {
            background-color: #27ae60;
        }
        
        .note {
            font-size: 14px;
            color: #777;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>Claude API密钥选择</h1>
            <a href="/admin" class="nav-button">管理界面</a>
        </header>
        
        <div class="card">
            <h2>选择API密钥</h2>
            
            <form id="key-form" method="POST">
                <input type="hidden" name="request_url" value="${requestUrl}">
                <div class="form-group">
                    <label for="key_name">选择可用的API密钥:</label>
                    <select id="key_name" name="key_name" required>
                        ${keyOptions}
                    </select>
                </div>
                
                <button type="submit">继续</button>
            </form>
            
            <p class="note">选择密钥后，您将被重定向到Claude服务。</p>
        </div>
    </div>
</body>
</html>`;
}

// 处理所有其他路径（反向代理）
async function handleDefault(request, env, ctx) {
    // ctx 参数保留以备将来可能的使用，例如执行上下文相关操作
    try {
        // 检查KV是否可用
        if (!env || !env.cla) {
            console.error('KV存储未配置');
            // 默认目标站点 - 仅开发用途
            const defaultOriginalWebsite = 'https://demo.fuclaude.com';
            return proxyRequest(request, defaultOriginalWebsite);
        }

        // 从KV存储中读取目标网站URL
        const originalWebsite = await env.cla.get('config:ORIGINAL_WEBSITE');
        if (!originalWebsite) {
            return new Response('配置错误：未找到目标网站URL', {
                status: 500,
                headers: { 'Content-Type': 'text/plain;charset=UTF-8' }
            });
        }

        // 处理特殊的OAuth回调路径（例如 /session/xyz123）
        const url = new URL(request.url);
        if (url.pathname.startsWith('/session/')) {
            console.log(`处理OAuth回调路径: ${url.pathname}`);
        }

        // 代理请求到目标网站
        return proxyRequest(request, originalWebsite);
    } catch (error) {
        return new Response(`代理请求错误: ${error.message}`, {
            status: 500,
            headers: { 'Content-Type': 'text/plain;charset=UTF-8' }
        });
    }
}

// 管理员界面HTML
const adminManageHtml = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API密钥管理</title>
    <link rel="stylesheet" href="/admin/manage/style.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>API密钥管理</h1>
            <a href="/user" class="nav-button">用户界面</a>
        </header>
        
        <div id="auth-container">
            <h2>管理员认证</h2>
            <div class="form-group">
                <label for="admin-password">管理员密码:</label>
                <input type="password" id="admin-password" placeholder="请输入管理员密码">
                <button id="login-button">登录</button>
            </div>
            <div id="auth-message" class="message"></div>
        </div>

        <div id="main-container" style="display: none;">
            <div id="message" class="message"></div>
            
            <div class="card">
                <h2>添加新密钥</h2>
                <form id="add-key-form">
                    <div class="form-group">
                        <label for="key-name">密钥名称:</label>
                        <input type="text" id="key-name" placeholder="例如：project_a_key1" required>
                    </div>
                    <div class="form-group">
                        <label for="api-key">API密钥:</label>
                        <input type="text" id="api-key" placeholder="例如：sk-ant-sid01--ABC...XYZ" required>
                    </div>
                    <div class="form-group">
                        <label for="description">描述:</label>
                        <input type="text" id="description" placeholder="描述这个密钥的用途">
                    </div>
                    <button type="submit" class="primary">添加密钥</button>
                </form>
            </div>
            
            <div class="card">
                <div class="title-with-button">
                    <h2>密钥列表</h2>
                    <button id="refresh-keys" class="refresh-button">刷新列表</button>
                </div>
                <div id="loading" class="loading">加载中...</div>
                <table id="keys-table">
                    <thead>
                        <tr>
                            <th>名称</th>
                            <th>API密钥</th>
                            <th>描述</th>
                            <th>添加时间</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="keys-list"></tbody>
                </table>
            </div>
            
            <div id="edit-modal" class="modal">
                <div class="modal-content">
                    <span class="close">&times;</span>
                    <h2>编辑密钥</h2>
                    <form id="edit-key-form">
                        <input type="hidden" id="edit-key-name">
                        <div class="form-group">
                            <label for="edit-api-key">API密钥:</label>
                            <input type="text" id="edit-api-key" placeholder="例如：sk-ant-sid01--ABC...XYZ" required>
                        </div>
                        <div class="form-group">
                            <label for="edit-description">描述:</label>
                            <input type="text" id="edit-description" placeholder="描述这个密钥的用途">
                        </div>
                        <div class="form-group">
                            <label for="edit-enabled">启用状态:</label>
                            <select id="edit-enabled">
                                <option value="true">启用</option>
                                <option value="false">禁用</option>
                            </select>
                        </div>
                        <button type="submit" class="primary">保存更改</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <script src="/admin/manage/script.js"></script>
</body>
</html>`;

// 管理员界面CSS
const adminManageCss = `* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
    background-color: #f5f7fa;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eaeaea;
}

h1 {
    color: #2c3e50;
    font-size: 24px;
}

h2 {
    color: #34495e;
    margin-bottom: 20px;
    font-size: 20px;
}

.nav-button {
    display: inline-block;
    background-color: #3498db;
    color: white;
    padding: 8px 16px;
    border-radius: 4px;
    text-decoration: none;
    font-size: 14px;
    transition: background-color 0.3s;
}

.nav-button:hover {
    background-color: #2980b9;
}

.card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 20px;
    margin-bottom: 30px;
}

.form-group {
    margin-bottom: 15px;
}

label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: #555;
}

input, select {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

button {
    padding: 10px 15px;
    border: none;
    border-radius: 4px;
    background-color: #e7e7e7;
    color: #333;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.3s;
}

button:hover {
    background-color: #d7d7d7;
}

button.primary {
    background-color: #2ecc71;
    color: white;
}

button.primary:hover {
    background-color: #27ae60;
}

button.danger {
    background-color: #e74c3c;
    color: white;
}

button.danger:hover {
    background-color: #c0392b;
}

table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
    background-color: white;
}

th, td {
    text-align: left;
    padding: 12px;
    border-bottom: 1px solid #eee;
}

th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #555;
}

tr:hover {
    background-color: #f5f5f5;
}

.status {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
}

.status-enabled {
    background-color: #e8f7f0;
    color: #27ae60;
}

.status-disabled {
    background-color: #fef5f5;
    color: #e74c3c;
}

.actions {
    display: flex;
    gap: 8px;
}

.action-button {
    padding: 4px 8px;
    font-size: 12px;
}

.title-with-button {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 15px;
}

.title-with-button h2 {
    margin-bottom: 0;
}

.refresh-button {
    background-color: #3498db;
    color: white;
    padding: 6px 12px;
    font-size: 12px;
    width: auto;
}

.refresh-button:hover {
    background-color: #2980b9;
}

.message {
    padding: 10px;
    margin-bottom: 20px;
    border-radius: 4px;
    display: none;
}

.success {
    display: block;
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.error {
    display: block;
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.loading {
    text-align: center;
    padding: 20px;
    color: #777;
}

.key-preview {
    width: 120px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.modal {
    display: none;
    position: fixed;
    z-index: 1;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.4);
}

.modal-content {
    background-color: #fefefe;
    margin: 10% auto;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
    width: 80%;
    max-width: 500px;
}

.close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover {
    color: black;
}`;

// 管理员界面JavaScript
const adminManageJs = `// 管理员密码和认证状态
let adminPassword = '';
let isAuthenticated = false;

// DOM元素
const authContainer = document.getElementById('auth-container');
const mainContainer = document.getElementById('main-container');
const adminPasswordInput = document.getElementById('admin-password');
const loginButton = document.getElementById('login-button');
const authMessage = document.getElementById('auth-message');
const messageDiv = document.getElementById('message');
const addKeyForm = document.getElementById('add-key-form');
const keyNameInput = document.getElementById('key-name');
const apiKeyInput = document.getElementById('api-key');
const descriptionInput = document.getElementById('description');
const keysTable = document.getElementById('keys-table');
const keysList = document.getElementById('keys-list');
const loadingDiv = document.getElementById('loading');
const refreshKeysButton = document.getElementById('refresh-keys');
const editModal = document.getElementById('edit-modal');
const closeModalBtn = document.querySelector('.close');
const editKeyForm = document.getElementById('edit-key-form');
const editKeyNameInput = document.getElementById('edit-key-name');
const editApiKeyInput = document.getElementById('edit-api-key');
const editDescriptionInput = document.getElementById('edit-description');
const editEnabledSelect = document.getElementById('edit-enabled');

// 生成默认密钥名称（格式：日期-序号）
function generateDefaultKeyName(existingKeys = []) {
    // 获取今天的日期并格式化
    const today = new Date();
    const year = today.getFullYear();
    const month = (today.getMonth() + 1).toString().padStart(2, '0');
    const day = today.getDate().toString().padStart(2, '0');
    const datePrefix = \`\${year}\${month}\${day}\`;
    
    console.log("=====================");
    console.log("生成密钥名称，日期前缀:", datePrefix);
    console.log("现有密钥数量:", existingKeys ? existingKeys.length : "existingKeys为空");
    console.log("existingKeys数据类型:", typeof existingKeys);
    if (existingKeys && existingKeys.length > 0) {
        console.log("第一个密钥:", JSON.stringify(existingKeys[0], null, 2));
    }
    
    // 查找当天已有的最大序号
    let maxNumber = 0;
    
    // 使用字符串分割方法检查密钥名称格式而不是正则表达式
    if (existingKeys && existingKeys.length > 0) {
        existingKeys.forEach((key, index) => {
            console.log(\`检查第\${index + 1}个密钥:\`, key ? key.name : "键对象为空");
            if (key && key.name) {
                console.log("检查密钥名称:", key.name);
                
                // 检查密钥名称是否以日期前缀开头并包含连字符
                if (key.name.startsWith(datePrefix + "-")) {
                    // 提取序号部分
                    const parts = key.name.split("-");
                    if (parts.length === 2) {
                        const numStr = parts[1];
                        const num = parseInt(numStr, 10);
                        console.log("提取的序号:", num);
                        if (!isNaN(num) && num > maxNumber) {
                            maxNumber = num;
                            console.log("更新最大序号为:", maxNumber);
                        }
                    } else {
                        console.log("密钥名称格式不符合'日期-序号'格式:", key.name);
                    }
                } else {
                    console.log("密钥名称不以当天日期开头:", key.name);
                }
            } else {
                console.log("密钥对象不包含name属性或为空");
            }
        });
    } else {
        console.log("无现有密钥或列表为空");
    }
    
    // 最大序号加1作为新序号
    const newNumber = maxNumber + 1;
    console.log("生成的新序号:", newNumber);
    
    // 返回日期-序号格式的名称
    const newName = \`\${datePrefix}-\${newNumber}\`;
    console.log("最终生成的名称:", newName);
    console.log("=====================");
    return newName;
}

// 设置默认密钥名称
function setDefaultKeyName(keys) {
    keyNameInput.value = generateDefaultKeyName(keys);
}

// 显示消息函数
function showMessage(message, isError = false) {
    messageDiv.textContent = message;
    messageDiv.className = 'message ' + (isError ? 'error' : 'success');
    
    // 5秒后自动隐藏
    setTimeout(() => {
        messageDiv.className = 'message';
    }, 5000);
}

// 显示认证消息函数
function showAuthMessage(message, isError = false) {
    authMessage.textContent = message;
    authMessage.className = 'message ' + (isError ? 'error' : 'success');
    
    // 5秒后自动隐藏
    setTimeout(() => {
        authMessage.className = 'message';
    }, 5000);
}

// API请求基础函数
async function apiRequest(method, path, body = null) {
    const headers = {
        'X-Admin-Password': adminPassword,
        'Content-Type': 'application/json'
    };
    
    const options = {
        method,
        headers
    };
    
    if (body && (method === 'POST' || method === 'PUT')) {
        options.body = JSON.stringify(body);
    }
    
    const response = await fetch(path, options);
    const data = await response.json();
    
    if (!response.ok) {
        throw new Error(data.error || '请求失败');
    }
    
    return data;
}

// 加载密钥列表
async function loadKeys() {
    loadingDiv.style.display = 'block';
    keysList.innerHTML = '';
    
    try {
        const keys = await apiRequest('GET', '/admin/api/keys');
        loadingDiv.style.display = 'none';
        
        console.log("API返回的keys数据:", JSON.stringify(keys));
        
        // 设置默认密钥名称
        setDefaultKeyName(keys);
        
        if (keys.length === 0) {
            keysList.innerHTML = '<tr><td colspan="6" style="text-align: center;">暂无密钥数据</td></tr>';
            return;
        }
        
        keys.forEach(key => {
            const tr = document.createElement('tr');
            
            // 格式化时间
            const date = new Date(key.addedTimestamp);
            const formattedDate = date.toLocaleDateString('zh-CN') + ' ' + date.toLocaleTimeString('zh-CN');
            
            // 显示API密钥的部分内容
            const apiKeyPreview = key.apiKey.substring(0, 10) + '...' + key.apiKey.substring(key.apiKey.length - 5);
            
            tr.innerHTML = \`
                <td>\${key.name}</td>
                <td class="key-preview" title="\${key.apiKey}">\${apiKeyPreview}</td>
                <td>\${key.description || '无描述'}</td>
                <td>\${formattedDate}</td>
                <td><span class="status \${key.enabled ? 'status-enabled' : 'status-disabled'}">\${key.enabled ? '启用' : '禁用'}</span></td>
                <td class="actions">
                    <button class="action-button edit-button" data-name="\${key.name}">编辑</button>
                    <button class="action-button danger delete-button" data-name="\${key.name}">删除</button>
                </td>
            \`;
            
            keysList.appendChild(tr);
        });
        
        // 添加编辑和删除按钮的事件监听
        document.querySelectorAll('.edit-button').forEach(button => {
            button.addEventListener('click', () => openEditModal(button.dataset.name));
        });
        
        document.querySelectorAll('.delete-button').forEach(button => {
            button.addEventListener('click', () => deleteKey(button.dataset.name));
        });
        
    } catch (error) {
        loadingDiv.style.display = 'none';
        showMessage('加载密钥列表失败: ' + error.message, true);
    }
}

// 添加新密钥
async function addKey(name, apiKey, description) {
    try {
        await apiRequest('POST', '/admin/api/keys', {
            name,
            apiKey,
            description
        });
        
        showMessage('密钥添加成功');
        addKeyForm.reset();
        await loadKeys(); // 重新加载密钥列表，会更新默认名称
    } catch (error) {
        showMessage('添加密钥失败: ' + error.message, true);
    }
}

// 打开编辑模态框
async function openEditModal(keyName) {
    try {
        // 获取所有密钥
        const keys = await apiRequest('GET', '/admin/api/keys');
        
        // 找到指定的密钥
        const key = keys.find(k => k.name === keyName);
        
        if (!key) {
            throw new Error('未找到该密钥');
        }
        
        // 填充表单
        editKeyNameInput.value = key.name;
        editApiKeyInput.value = key.apiKey;
        editDescriptionInput.value = key.description || '';
        editEnabledSelect.value = key.enabled.toString();
        
        // 显示模态框
        editModal.style.display = 'block';
    } catch (error) {
        showMessage('获取密钥信息失败: ' + error.message, true);
    }
}

// 更新密钥
async function updateKey(name, apiKey, description, enabled) {
    try {
        await apiRequest('PUT', \`/admin/api/keys/\${name}\`, {
            apiKey,
            description,
            enabled: enabled === 'true'
        });
        
        showMessage('密钥更新成功');
        editModal.style.display = 'none';
        await loadKeys();
    } catch (error) {
        showMessage('更新密钥失败: ' + error.message, true);
    }
}

// 删除密钥
async function deleteKey(name) {
    if (!confirm(\`确定要删除密钥 "\${name}" 吗？\`)) {
        return;
    }
    
    try {
        await apiRequest('DELETE', \`/admin/api/keys/\${name}\`);
        showMessage('密钥删除成功');
        await loadKeys();
    } catch (error) {
        showMessage('删除密钥失败: ' + error.message, true);
    }
}

// 登录函数
async function login(password) {
    try {
        // 设置密码
        adminPassword = password;
        
        // 尝试获取密钥列表，如果成功则说明密码正确
        await apiRequest('GET', '/admin/api/keys');
        
        // 登录成功
        isAuthenticated = true;
        authContainer.style.display = 'none';
        mainContainer.style.display = 'block';
        
        // 加载密钥列表
        await loadKeys();
    } catch (error) {
        isAuthenticated = false;
        adminPassword = '';
        showAuthMessage('认证失败: ' + error.message, true);
    }
}

// 事件监听
loginButton.addEventListener('click', () => {
    const password = adminPasswordInput.value.trim();
    if (!password) {
        showAuthMessage('请输入管理员密码', true);
        return;
    }
    
    login(password);
});

// 回车键登录
adminPasswordInput.addEventListener('keypress', (e) => {
    if (e.key === 'Enter') {
        loginButton.click();
    }
});

// 添加密钥表单提交
addKeyForm.addEventListener('submit', (e) => {
    e.preventDefault();
    
    const name = keyNameInput.value.trim();
    const apiKey = apiKeyInput.value.trim();
    const description = descriptionInput.value.trim();
    
    if (!name || !apiKey) {
        showMessage('名称和API密钥为必填项', true);
        return;
    }
    
    addKey(name, apiKey, description);
});

// 编辑密钥表单提交
editKeyForm.addEventListener('submit', (e) => {
    e.preventDefault();
    
    const name = editKeyNameInput.value.trim();
    const apiKey = editApiKeyInput.value.trim();
    const description = editDescriptionInput.value.trim();
    const enabled = editEnabledSelect.value;
    
    if (!name || !apiKey) {
        showMessage('名称和API密钥为必填项', true);
        return;
    }
    
    updateKey(name, apiKey, description, enabled);
});

// 关闭模态框
closeModalBtn.addEventListener('click', () => {
    editModal.style.display = 'none';
});

// 点击模态框外部关闭
window.addEventListener('click', (e) => {
    if (e.target === editModal) {
        editModal.style.display = 'none';
    }
});

// 刷新按钮点击事件
refreshKeysButton.addEventListener('click', async () => {
    await loadKeys();
});`;

// 用户界面HTML
const userChatHtml = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Claude API密钥选择</title>
    <style>
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }
        
        .container {
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
        }
        
        header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eaeaea;
        }
        
        h1 {
            color: #2c3e50;
            font-size: 24px;
        }
        
        .nav-button {
            display: inline-block;
            background-color: #3498db;
            color: white;
            padding: 8px 16px;
            border-radius: 4px;
            text-decoration: none;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        
        .nav-button:hover {
            background-color: #2980b9;
        }
        
        .card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 30px;
        }
        
        h2 {
            color: #34495e;
            margin-bottom: 20px;
            font-size: 20px;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #555;
        }
        
        input, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        button {
            width: 100%;
            padding: 12px;
            background-color: #2ecc71;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        button:hover {
            background-color: #27ae60;
        }
        
        .message {
            padding: 10px;
            margin-bottom: 20px;
            border-radius: 4px;
            display: none;
        }
        
        .error {
            display: block;
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>Claude API密钥选择</h1>
            <a href="/admin" class="nav-button">管理界面</a>
        </header>
        
        <div class="card">
            <h2>用户认证</h2>
            <div class="message" id="message"></div>
            
            <form id="chat-form" method="POST">
                <div class="form-group">
                    <label for="site_password">访问密码:</label>
                    <input type="password" id="site_password" name="site_password" required placeholder="请输入网站访问密码">
                </div>
                
                <button type="submit">下一步</button>
            </form>
        </div>
    </div>
</body>
</html>`;

// 导出Worker处理函数
export default {
    fetch: handleRequest
}; 